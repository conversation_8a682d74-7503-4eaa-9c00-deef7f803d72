#include <Windows.h>
#include "MinHook.h"
#include <Psapi.h>
#pragma comment(lib, "Psapi.lib")

typedef void(__fastcall* tOriginalFunc)(void* ebx);
tOriginalFunc originalFunc = nullptr;

// العنوان الثابت المستخدم في السكربت
#define BASE_ADDRESS (DWORD)GetModuleHandleA("Conquer.exe")
#define PTR_MONSTER   (*(DWORD*)(BASE_ADDRESS + 0x14F1D80))

// المنطق المحول من سكربت Cheat Engine
void __fastcall HookedFunc(void* ebx)
{
    DWORD eax = *(DWORD*)((DWORD)ebx + 0x260);
    DWORD monsterAttackID = *(DWORD*)(PTR_MONSTER + 0x450);

    if (eax == monsterAttackID)
    {
        DWORD motion = *(DWORD*)(PTR_MONSTER + 0x42C);
        if (motion == 6 || motion == 0)
        {
            DWORD attackState = *(DWORD*)(PTR_MONSTER + 0x428);
            if (attackState == 35)
            {
                int id = *(int*)((DWORD)ebx + 0x268);

                // الوحوش المستثناة
                if (id == 3992 || id == 3986 || id == 3984 || id == 3983 ||
                    id == 3982 || id == 3981 || id == 3980 || id == 3979)
                {
                    originalFunc(ebx);
                    return;
                }

                // تفعيل One Hit
                *(int*)((DWORD)ebx + 0x130) = 1056;
            }
        }
    }
    originalFunc(ebx);
}

// AOB بسيط (يمكن تطويره لاحقاً)
uintptr_t FindPattern(uintptr_t base, size_t size, const char* pattern, const char* mask)
{
    for (uintptr_t i = base; i < base + size; i++)
    {
        bool found = true;
        for (size_t j = 0; mask[j]; j++)
        {
            if (mask[j] == 'x' && pattern[j] != *(char*)(i + j))
            {
                found = false;
                break;
            }
        }
        if (found)
            return i;
    }
    return 0;
}

DWORD WINAPI MainThread(LPVOID)
{
    HMODULE hModule = GetModuleHandleA("Conquer.exe");
    MODULEINFO modInfo{};
    GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(MODULEINFO));

    const char* pattern = "\x81\xBB\x60\x02\x00\x00\x3F\x42\x0F\x00\x77\x0D";
    const char* mask = "xxxxxxxxxxxx";
    uintptr_t address = FindPattern((uintptr_t)hModule, modInfo.SizeOfImage, pattern, mask);

    if (address == 0)
    {
        MessageBoxA(0, "AOB Not Found", "Error", MB_OK | MB_ICONERROR);
        return 0;
    }

    MH_Initialize();
    MH_CreateHook((LPVOID)address, &HookedFunc, reinterpret_cast<LPVOID*>(&originalFunc));
    MH_EnableHook((LPVOID)address);

    MessageBoxA(0, "One Hit Hook Enabled", "Conquer Hack", MB_OK | MB_ICONINFORMATION);
    return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    if (ul_reason_for_call == DLL_PROCESS_ATTACH)
        CreateThread(0, 0, MainThread, 0, 0, 0);
    return TRUE;
}
